{"name": "office-app", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "serve": "vite --host", "build": "vite build", "preview": "vite preview", "format": "prettier --write \"src/**/*.{js,vue,css,scss}\"", "lint": "eslint --ext .js,.vue --ignore-path .gitignore --fix src"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "@tpp/htm-x": "^5.8.2", "@vant/touch-emulator": "^1.4.0", "animate.css": "4.1.1", "ant-design-vue": "1.7.8", "axios": "^0.26.1", "clipboard": "^2.0.11", "crypto-js": "^4.2.0", "echarts": "^5.6.0", "echarts-liquidfill": "^3.1.0", "element-resize-detector": "^1.2.4", "element-ui": "^2.15.14", "html2canvas": "^1.4.1", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "md5": "^2.3.0", "mitt": "^3.0.1", "moment": "^2.30.1", "pdfjs-dist": "3.4.120", "pinia": "^2.1.7", "sortablejs": "1.15.3", "uuid": "^11.1.0", "vant": "^2.12.53", "vconsole": "^3.15.0", "vite-plugin-commonjs": "^0.10.1", "vue": "^2.7.16", "vue-dompurify-html": "^4.1.4", "vue-ls": "^4.2.0", "vue-markdown": "^2.2.4", "vue-router": "^3.6.5", "vue-seamless-scroll": "^1.1.23", "vuex": "^3.6.2"}, "devDependencies": {"@vitejs/plugin-vue2": "^2.3.1", "autoprefixer": "^10.4.19", "babel-plugin-component": "^1.1.1", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-plugin-vue": "^8.7.1", "less": "^4.3.0", "postcss-px-to-viewport-8-plugin": "^1.2.5", "sass": "^1.74.1", "sharp": "^0.33.5", "vite": "5.2.8", "vite-plugin-image-optimizer": "^1.1.8"}}