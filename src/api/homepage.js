import { axios } from '@/utils/request'
import Vue from 'vue'
const baseUrl = import.meta.env.VITE_APP_SITUATION_BASE_URL

const Api = {
  SlideList: baseUrl + '/rotation/quickRotation/list',
  SlideAllList: baseUrl + '/rotation/quickRotation/allList',
  PopWords: baseUrl + '/ytpopword/getPopWords',
  HomeList: baseUrl + '/app/system/queryHasRoleTopicList', //首页专题列表
  Token: baseUrl + '/app/system/getToken',
  topicList: '/cockpit/app/menu/queryAppMenuByWorkbenchCode?workbenchCode=mobile'
}

// 轮播图
export function getSlideListApi() {
  return axios({
    url: Api.SlideList,
    method: 'get'
  })
}

// 轮播图
export function getSlideAllListApi() {
  return axios({
    url: Api.SlideAllList,
    method: 'get'
  })
}

// 热词
export function getPopWordsApi(params) {
  return axios({
    url: Api.PopWords,
    method: 'get',
    params
  })
}

// 城市通 获取 token
export function getTokenApi(userGuid) {
  return axios({
    url: Api.Token,
    method: 'get',
    params: {
      userGuid
    }
  })
}

// 山东通 获取 token
export function getSdtTokenApi(code) {
  return axios({
    url: baseUrl + '/sdtApp/loginByCode',
    method: 'get',
    params: {
      code
    }
  })
}

// 山东通 获取 签名等数据
export function getJsSdkParamsApi(url) {
  return axios({
    url: baseUrl + '/sdtApp/getJsSdkParams',
    method: 'get',
    params: {
      url
    }
  })
}

// 首页专题列表
export function getHomeListApi(params) {
  return axios({
    url: Api.HomeList,
    method: 'get',
    params,
    headers: { 'Cache-Control': 'no-cache' }
  })
}

export function getTopicListApi(params) {
  return axios({
    url: Api.topicList,
    method: 'get',
    params
  })
}

//保存当前用户的guid，城市通特性
export function saveGuid(userGuid) {
  Vue.ls.set('USERGUID', userGuid) //保存用户的guid
}
