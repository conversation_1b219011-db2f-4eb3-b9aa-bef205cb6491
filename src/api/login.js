import { axios } from '@/utils/request.js'

/**
 * login func
 * parameter: {
 *     username: '',
 *     password: '',
 *     remember_me: true,
 *     captcha: '12345'
 * }
 * @param parameter
 * @returns {*}
 */
export function login(parameter) {
  return axios({
    // url: '/sys/login',
    url: `${import.meta.env.VITE_APP_SITUATION_BASE_URL}captcha/login`,
    method: 'post',
    data: parameter
  })
}

export function phoneLogin(parameter) {
  return axios({
    url: `${import.meta.env.VITE_APP_SITUATION_BASE_URL}sys/phoneLogin`,
    method: 'post',
    data: parameter
  })
}



export function logout(logoutToken) {
  return axios({
    url: `${import.meta.env.VITE_APP_SITUATION_BASE_URL}sys/logout`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      'X-Access-Token': logoutToken
    }
  })
}

/**
 * 第三方登录
 * @param token
 * @param thirdType
 * @returns {*}
 */
export function thirdLogin(token, thirdType) {
  return axios({
    url: `${import.meta.env.VITE_APP_SITUATION_BASE_URL}sys/thirdLogin/getLoginUser/${token}/${thirdType}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
