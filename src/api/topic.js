import { axios } from '@/utils/request'
const baseUrl = import.meta.env.VITE_APP_SITUATION_BASE_URL

const Api = {
  TopicInfo: '/topic/ytTopic/getInitDataNew',
  TopicInfoList: baseUrl + '/app/system/queryHasRoleIndexList',
  GetFirstTopic: '/topic/ytTopic/getFirstTopicNew'
}

// 获取专题信息
export function getTopicInfoApi(topicId) {
  return axios({
    url: Api.TopicInfo,
    method: 'get',
    params: {
      id: topicId
    }
  })
}

// 获取专题信息,卓数版本
export function getTopicInfoListApi(topicId, userGuid) {
  return axios({
    url: Api.TopicInfoList,
    method: 'get',
    params: {
      topicId: topicId,
      userGuid: userGuid
    }
  })
}

// 获取一级分类专题
export function getFirstTopicApi() {
  return axios({
    url: Api.GetFirstTopic,
    method: 'get'
  })
}
