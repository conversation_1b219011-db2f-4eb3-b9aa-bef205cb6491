import { getAction, postAction } from './manage'
const baseUrl = import.meta.env.VITE_APP_SITUATION_BASE_URL

// 根据 id 查询指标
export function getIndexInfoByIdApi(indexId) {
  return getAction(baseUrl+'/app/system/queryDataByIndexId', { indexId })
}

// 根据接口 id 查询指标
export function queryDataByApiIdApi(data) {
  return postAction(baseUrl+'/app/system/queryDataByApiId', data)
}

// 通过字典 code 查询字典标签
export function getDictLabelByCodeApi(dictCode) {
  return getAction(baseUrl+`/app/system/queryDictItem/${dictCode}`)
}

// 重点指标专题
export function getIndicatorsFieldApi() {
  return getAction(baseUrl+'/indicators/ytOrgIndicators/type')
}

// 重点指标列表
export function getIndicatorsInfoListApi(params) {
  return getAction(baseUrl+'/indicators/ytOrgIndicators/index/listIndex', params)
}
