export function getDataProperty(data, index) {
  const res = index.map((element) => {
    return {
      value1: data[element]?.dataSource,
      value2: data[element]?.stSyncTime,
      value3: data[element]?.frequency,
      value4: data[element]?.dpDataTime
    }
  })
  return res
}

// 对于pad端独有的卡片，一般需调用月度、年度切换接口，采用接口数据的属性名
export function getDataPropertyOfNewCard(data) {
  return {
    value1: data[0].data_source,
    value2: data[0].st_sync_time,
    value3: data[0].frequency,
    value4: data[0].dp_data_time
  }
}
