import { postAction,getAction } from '@/api/manage'

const baseUrl = import.meta.env.VITE_APP_SITUATION_BASE_URL

export const ecnomicOperation = {
  list: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_sy_jjyx_gk`, data)
}
export const cultureTourism = {
  list: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_sy_wlly_gk`, data)
}
export const ecologicalEnvironment = {
  getItem1: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_sy_sthj_zyhbdc`, data),
  getItem2: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_sy_sthj_hhzxxs`, data),
  getDetail1: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_sy_sthj_zyhbdcxq`, data),
  getDetail2: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_sy_sthj_hhzxxsxq`, data)
}
export const getLowCarbonEnergy = {
  list: (data = {}) => getAction(`${baseUrl}/topic/data/listBySql/ydd_sy_nydt_gk_sql`, data)
}
export const getResidentEmployment = {
  list: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_sy_jmjy_gk`, data)
}
