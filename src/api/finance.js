import { getAction } from '@/api/manage'
const baseUrl = import.meta.env.VITE_APP_SITUATION_BASE_URL

// 获取财政概况数据
export function getFinanceOverviewData() {
  return getAction(`${baseUrl}topic/data/listAllBySql/ydd_czqk_gk`)
}
// 获取央地结算数据
export function getCentralAndLocalSettlementData() {
  return getAction(`${baseUrl}topic/data/listAllBySql/ydd_czqk_zyysd`)
}
// 获取往年财政回顾数据
export function getAnnualFinancialReviewData() {
  return getAction(`${baseUrl}topic/data/listAllBySql/ydd_czqk_czhg`)
}
// 获取山东省一般公共收入趋势数据
export function getGeneralPublicRevenueInShanDongTrendData(type) {
  return getAction(`${baseUrl}topic/data/listAllBySql/ydd_czqk_ybyssrqsqk?type=${type}`)
}
// 获取山东省一般公共支出趋势数据
export function getGeneralPublicExpenditureInShanDongTrendData(type) {
  return getAction(`${baseUrl}topic/data/listAllBySql/ydd_czqk_ybyszcqsqk?type=${type}`)
}
// 获取鲁苏浙粤一般公共预算数据
export function getGeneralPublicBudgetForTopFourProvincesData() {
  return getAction(`${baseUrl}topic/data/listAllBySql/ydd_czqk_lszyedb`)
}
// 获取分税种收入数据
export function getRevenueByTaxCategoryData() {
  return getAction(`${baseUrl}topic/data/listAllBySql/ydd_czqk_ljsssr_xq`)
}
// 获取三保实际支出数据
export function getActualExpenditureOfTheThreeGuaranteesData() {
  return getAction(`${baseUrl}topic/data/listAllBySql/ydd_czqk_sbshzc`)
}
// 获取各项支出数据
export function getDetailsOfExpenditureCategoryData() {
  return getAction(`${baseUrl}topic/data/listAllBySql/ydd_czqk_mszc_xq`)
}
// 获取年度各支出项目预算完成率
export function getAnnualExpenditureProjectBudgetCompletionRateData() {
  return getAction(`${baseUrl}topic/data/listAllBySql/ydd_czqk_gzcxmyswcl`)
}
// 获取全省各市一般公共预算数据
export function getGeneralPublicBudgetForAllCitiesInShanDongData() {
  return getAction(`${baseUrl}topic/data/listAllBySql/ydd_czqk_pad_ybggyssrzc_gk`)
}
// 获取全省一般公共预算(地市维度)详情数据
export function getGeneralPublicBudgetForAllCitiesInShanDongDetailsData() {
  return getAction(`${baseUrl}topic/data/listAllBySql/ydd_czqk_pad_ybggyssryzc`)
}
