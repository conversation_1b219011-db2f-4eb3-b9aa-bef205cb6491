import { getAction } from '@/api/manage'
import store from '@/store/index'
const baseUrl = import.meta.env.VITE_APP_SITUATION_BASE_URL

export function changeDataProperty(id, name) {
  // return getAction(`${baseUrl}/topic/data/listAllBySql/ydd_topic_date_switch?id=${id}&name=${name}`)
  return getAction(`${baseUrl}topic/data/listAllBySql/ydd_topic_date_switch_dropdown_tabandtime?id=${id}&name=${name}`)
}
export function changeDataPropertyNew(id, name, time) {
  return getAction(
    `${baseUrl}/topic/data/listAllBySql/ydd_topic_date_switch_dropdown?id=${id}&name=${name}&indexTime=${time}`
  )
}

export async function changeVuex(...args) {
  const [index, name, time] = args.slice(0)
  const res = await (time
    ? changeDataPropertyNew(store.state.DataSourceList[index].topicId, name, time)
    : changeDataProperty(store.state.DataSourceList[index].topicId, name))
  store.dispatch('actionSwitchDate', { res: res.result[0], id: index })
}
