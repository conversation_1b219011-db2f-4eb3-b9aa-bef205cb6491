import { getAction, postAction } from '@/api/manage'

const baseUrl = import.meta.env.VITE_APP_SITUATION_BASE_URL

export const getAreaList = {
  getTotalArea: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_rk_dqmj_zmj`, data),
  getCityStandardSort: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_rk_dqmj_gdsmj`, data),
  getAreaSort: (data = {}) => getAction(`${baseUrl}topic/data/listAllBySql/ydd_rk_dqmj_gdsmjbymj`, data)
}
export const getPopulationList = {
  getPopulationDefaultList: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_rk_rksl_gdsrksl`, data),
  getPopulationBaseSituation: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_rk_rksl_jbqk`, data),
  getPopulationRegionSituation: (data = {}) =>
    postAction(`${baseUrl}/topic/data/listAllData/ydd_rk_rksl_gdsrksl`, data),
  getPopulationRegisteredSituation: (data = {}) =>
    getAction(`${baseUrl}/topic/data/listAllBySql/ydd_rk_rksl_gdsrkslbyhj`, data),
  getPopulationRegisteredSituationAscend: (data = {}) =>
    getAction(`${baseUrl}/topic/data/listAllBySql/ydd_rk_rksl_gdsrkslbyhjsx`, data),
  getPopulationPermanentSituation: (data = {}) =>
    getAction(`${baseUrl}/topic/data/listAllBySql/ydd_rk_rksl_gdsrkslbycz`, data),
  getPopulationPermanentSituationAscend: (data = {}) =>
    getAction(`${baseUrl}/topic/data/listAllBySql/ydd_rk_rksl_gdsrkslbyczsx`, data),
  getUrbanizationRate: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_rk_rksl_gdspjczhl`, data),
  getPopulationDensity: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_rk_rksl_rkmdfb`, data),
  getPopulationDensityTop: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_rk_rksl_rkmdfbpm`, data),
  getPopulationDensitybtm: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_rk_rksl_gdspjczhlpx`, data),
  getPopulationData: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_rk_kp_jbxx`, data)
}
export const getAllDataList = {
  getData: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_rk_kp_jbxx_sql`, data),
  getAreaData: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_rk_kp_mjxx`, data)
}
