import { getAction } from '@/api/manage'
const baseUrl = import.meta.env.VITE_APP_SITUATION_BASE_URL

export const socialAssistance = {
  getCardData: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_shbz_shjz_kp`, data),
  getCardDataBelow: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_shbz_shjz_ncdbbz`, data),
  getDifficultGroupData: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_shbz_shjz_knqzjzbz_tbzzl`, data),
}
export const socialSecurityBenefits = {
  getCardData: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_shbz_sbdy_kp`, data),
  getCardDataBelow: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_shbz_sbdy_cbbh`, data),
  getEndowmentInsuranceData: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_shbz_sbdy_jbqk`, data),
}
export const assistingEnterprises = {
  getCardData: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_shbz_yqsk_kp`, data),
  getCardDataBelow: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_shbz_yqsk_wdjygw`, data),
  getJobExpansionSubsidyData: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_shbz_yqsk_ycxkgbz`, data),
}
export const elderlyCareServices = {
  getCardData: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_shbz_ylfw_kp`, data),
  getDetailedData: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_shbz_ylfw_jbqk`, data),
}