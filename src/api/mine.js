import { axios } from '@/utils/request'
const baseUrl = import.meta.env.VITE_APP_SITUATION_BASE_URL

const Api = {
  GetCollectionList: baseUrl+'/index/collection/listCollcetions',
  CommentList: '/index/comment/list',
  AddCollection: baseUrl+'/index/collection',
  DeleteCollection: baseUrl+'/index/collection/mDelete/',
  AddComment: '/index/comment',
  GetCollectionStatus: '/index/collection/status/',
  GetCollectionIds: '/index/collection/listId',
  GetCollectionTopic: baseUrl+'/index/collection/topic'
}

// 收藏列表
export function getCollectionListApi(params) {
  return axios({
    url: Api.GetCollectionList,
    method: 'get',
    params
  })
}

// 收藏列表
export function getCollectionTopicApi() {
  return axios({
    url: Api.GetCollectionTopic,
    method: 'get'
  })
}

// 查询某个指标是否收藏
export function getCollectionStatusApi(id) {
  return axios({
    url: Api.GetCollectionStatus + id,
    method: 'get'
  })
}

// 查询所有收藏的指标id
export function getCollectionIdsApi() {
  return axios({
    url: Api.GetCollectionIds,
    method: 'get'
  })
}

// 新增收藏
export function addCollectionApi(data) {
  return axios({
    url: Api.AddCollection,
    method: 'POST',
    data
  })
}

// 删除收藏
export function deleteCollectionApi(id) {
  return axios({
    url: Api.DeleteCollection + id,
    method: 'get'
  })
}

// 评论列表
export function getCommentListApi(params) {
  return axios({
    url: Api.CommentList,
    method: 'get',
    params
  })
}

// 新增评论
export function addCommentApi(data) {
  return axios({
    url: Api.AddComment,
    method: 'POST',
    data
  })
}
