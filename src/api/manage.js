import { axios } from '@/utils/request'

// const YQUrl = 'http://10.221.19.12:8500/'
// export default YQUrl

export function postAction(url, parameter) {
  return axios({
    url: url,
    method: 'post',
    data: parameter
  })
}
export function httpAction(url, parameter, method) {
  return axios({
    url: url,
    method: method,
    data: parameter
  })
}

// put
export function putAction(url, parameter) {
  return axios({
    url: url,
    method: 'post',
    data: parameter
  })
}

// get
export function getAction(url, parameter) {
  return axios({
    url: url,
    method: 'get',
    params: parameter
  })
}

// get
export function getActionYq(url, parameter) {
  return axios({
    url: url,
    method: 'get',
    params: parameter
  })
}

export function getActionWithToken(url, parameter) {
  return axios({
    url: url,
    method: 'get',
    headers: { Authorization: `Bearer 1` },
    params: parameter
  })
}
export function getActionWithToken2(url, parameter, token) {
  return axios({
    url: url,
    method: 'GET',
    params: parameter,
    headers: {
      'X-Access-Token': token
    }
  })
}
export function deleteActionWithToken(url, parameter, token) {
  return axios({
    url: url,
    method: 'DELETE',
    params: parameter,
    headers: {
      'X-Access-Token': token
    }
  })
}

// deleteAction
export function deleteAction(url, parameter) {
  return axios({
    url: url,
    method: 'get',
    params: parameter
  })
}

/**
 * 获取文件服务访问路径
 * @param avatar
 * @param subStr
 * @returns {*}
 */
export function getFileAccessHttpUrl(avatar, subStr) {
  if (!subStr) subStr = 'http'
  if (avatar && avatar.startsWith(subStr)) {
    return avatar
  } else {
    if (avatar && avatar.length > 0 && avatar.indexOf('[') === -1) {
      return import.meta.env.VITE_APP_API_BASE_URL + '/cockpit/' + avatar
    }
  }
}
