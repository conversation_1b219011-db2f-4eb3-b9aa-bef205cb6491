import { getAction, postAction } from '@/api/manage'

const baseUrl = import.meta.env.VITE_APP_SITUATION_BASE_URL

export const getTopCardData = {
  list: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_jy_jyxs_jbqk`, data)
}

export const urbanEmployment = {
  getCountryProportion: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_jy_czjy_xzjygm`, data),
  getAddEmploymentNumbers: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_jy_czjy_xzjyrsnd`, data),
  getAddEmploymentNumbers2: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_jy_czjy_xzjyrsnd_sql?type=1`, data),
  newList: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_jy_czjy_xzjyrsnd_sql?type=0`, data)
}
export const urbanEmployment2 = {
  list: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_jy_czjy_xzjyrsny`, data)
}
export const graduateEmployment = {
  // list: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_jy_gxbysjy_jbqk`, data)
  // list: (data = {}) => postAction(`${baseUrl}/topic/data/listAllBySql/ydd_jy_gxbysjy_jbqk_new_sql`, data)
  // list: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_jy_gxbysjy_jbqk_new`, data)
  list: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_jy_gxbysjy_jbqk_new_sql?type=sl`, data),
  list2: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_jy_gxbysjy_jbqk_new_sql?type=lsqk`, data),
  newList: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_jy_gxbysjy_jbqk_new_sql?type=qx`, data)
}
export const graduateEmployment2 = {
  list: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/yyd_jy_jy_byssllszb`, data)
}
export const urbanemployment3 = {
  list: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_jy_czjy_xzjyrsyd`, data)
}
export const ruralEmployment = {
  // list: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_jy_ncldljy_jbjy`, data)
  // list: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_jy_ncldljy_new`, data)
  list: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_jy_ncldljy_new_sql`, data)
}
export const urbanUnemployment = {
  list: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_jy_czsy_jbqk`, data),
  getUnemPloymentYear: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_jy_czsy_lnsyrs`, data),
  getRateList: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_jy_czsy_lnsyl`, data),
  getMonthData: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_jy_czsy_jysyl`, data)
}

export function monthlyUnemployment() {
  return getAction(`${baseUrl}/topic/data/listAllBySql/ydd_jy_czsy_lysyl`)
}
export const publicWelfare = {
  // list: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_jy_gyxgw_jbqk`, data)
  list: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_jy_gyxgw_new_sql`, data)
}
export const appropriateLabour = {
  // list: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_jy_slldl_jbqk`, data)
  list: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_jy_slldl_new_sql`, data)
}
export const prediction = {
  list: (data = {}) => postAction(`${baseUrl}/topic/data/listAllData/ydd_jy_yc_jbqk`, data)
}
