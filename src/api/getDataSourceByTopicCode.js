export function getDataSourceByTopicCode(data, topicCodeArr) {
  let dataSourceArr
  if (typeof topicCodeArr === 'object') {
    // 字符串数组
    dataSourceArr = topicCodeArr.map((item) => {
      const obj = data.filter((ele) => ele.topicCode === item)
      return {
        value1: obj[0]?.dataSource,
        value2: obj[0]?.stSyncTime,
        value3: obj[0]?.frequency,
        value4: obj[0]?.dpDataTime
      }
    })
  } else if (typeof topicCodeArr === 'string') {
    // 单个字符串
    const obj = data.filter((ele) => ele.topicCode === topicCodeArr)
    dataSourceArr = {
      value1: obj[0]?.dataSource,
      value2: obj[0]?.stSyncTime,
      value3: obj[0]?.frequency,
      value4: obj[0]?.dpDataTime
    }
  } else {
    return '编码格式应为数组或字符串'
  }
  return dataSourceArr
}
