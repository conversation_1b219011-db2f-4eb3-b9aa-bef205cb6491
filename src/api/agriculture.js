import { getAction, postAction } from '@/api/manage'

const baseUrl = import.meta.env.VITE_APP_SITUATION_BASE_URL

// 卡片
export function getCardData(type) {
  return getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_kp?type=${type}`)
}

// 农业农村
export function getProvinceAgriculture(indexTime) {
  return getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_qsnyncqk?indexTime=${indexTime}`)
}
export function getProvinceRuralIncome(indexTime) {
  return getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_qsncrjsr?indexTime=${indexTime}`)
}
export const getTime = {
  time1: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_qsnyncqk_time`, data),
  time2: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_qsncrjsr_time`, data),
  time3: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_nlmy_zcz_time`, data),
  time4: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_lssc_zcl_time`, data),
  time5: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_lssc_sccl_time`, data),
  time6: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_ncpck_cke_time`, data),
  time7: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_ncpck_ckezs_time`, data),
  time8: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_ncpck_qgbz_time`, data),
  time9: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_xdgxny_qk_time`, data),
  time10: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_xdgxny_qy_time`, data)
}
// 乡村振兴
export const ruralRevitalization = {
  list: (data = {}) => getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_xczx_gjqk`, data)
}
// 农林牧渔业
export function getNlmy(indexTime) {
  return getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_nlmy_zcz?indexTime=${indexTime}`)
}

// 粮食与蔬菜
export function getGrainYield(type, indexTime) {
  return getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_lssc_zcl?indexTime=${indexTime}&type=${type}`)
}
export function getVegetableYield(indexTime) {
  return getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_lssc_sccl?indexTime=${indexTime}`)
}

// 农产品出口
export function getAgriculturalProductsExport(indexTime) {
  return getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_ncpck_cke?indexTime=${indexTime}`)
}
export function getAgriculturalProductsExportGrowth(indexTime) {
  return getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_ncpck_ckezs?indexTime=${indexTime}`)
}
export function getAgriculturalProductsExportProportion(indexTime) {
  return getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_ncpck_qgbz?indexTime=${indexTime}`)
}

// 现代高效农业
export function getIndustrialCluster(indexTime) {
  return getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_xdgxny_qk?indexTime=${indexTime}`)
}
export function getAgriculturalProductsProcessingCompany(type, indexTime) {
  return getAction(`${baseUrl}/topic/data/listAllBySql/ydd_nync_xdgxny_qy?indexTime=${indexTime}&type=${type}`)
}
