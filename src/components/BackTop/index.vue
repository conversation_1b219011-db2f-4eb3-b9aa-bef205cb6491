<template>
  <transition name="van-fade">
    <div v-show="visible" :style="{ bottom: styleBottom, right: styleRight }" class="back-top" @click.stop="onClick">
      <slot>
        <van-button class="back-top__button">
          <van-icon name="back-top" />
        </van-button>
      </slot>
    </div>
  </transition>
</template>

<script>
import throttle from 'lodash/throttle'

// 设置滚动动画效果
const cubic = (value) => Math.pow(value, 3)
const easeInOutCubic = (value) => (value < 0.5 ? cubic(value * 2) / 2 : 1 - cubic((1 - value) * 2) / 2)

export default {
  name: 'BackTop',
  props: {
    visibilityHeight: {
      type: Number,
      default: 200
    },
    bottom: {
      type: Number,
      default: 50
    },
    right: {
      type: Number,
      default: 50
    }
  },
  data() {
    return {
      visible: false // 控制按钮显示
    }
  },
  computed: {
    styleBottom() {
      return `${this.bottom}px`
    },
    styleRight() {
      return `${this.right}px`
    }
  },
  mounted() {
    this.throttledScrollHandler = throttle(this.onScroll)
    document.addEventListener('scroll', this.throttledScrollHandler)
  },
  beforeDestroy() {
    document.removeEventListener('scroll', this.throttledScrollHandler)
  },
  methods: {
    onClick(e) {
      this.scrollToTop()
      this.$emit('click', e)
    },
    onScroll() {
      const scrollTop = document.documentElement.scrollTop
      this.visible = scrollTop > this.visibilityHeight
    },
    scrollToTop() {
      const el = document.documentElement // 滚动对象
      const beginTime = Date.now() // 开始滚动的时间
      const beginValue = el.scrollTop // 滚动距离
      // 执行动画
      const rAF = window.requestAnimationFrame || ((func) => setTimeout(func, 16))
      // 更新动画的回调函数
      const frameFunc = () => {
        // 控制动画进度
        const progress = (Date.now() - beginTime) / 500
        // progress 大于 1，则滚动动画执行完成，滚动条回到顶部
        if (progress < 1) {
          el.scrollTop = beginValue * (1 - easeInOutCubic(progress))
          rAF(frameFunc)
        } else {
          el.scrollTop = 0
        }
      }
      // 回调，持续执行滚动动画
      rAF(frameFunc)
    }
  }
}
</script>
