<template>
  <div class="query-search">
    <van-field
      ref="input"
      v-model="current"
      :label="name"
      placeholder="请输入"
      @blur="handleClick"
      @keyup.enter="handleClick"
    />
  </div>
</template>

<script>
import { ref, getCurrentInstance } from 'vue'
import { jsCodeF } from '@/utils'

export default {
  name: 'IndicatorQueryInput',
  props: {
    name: { type: String, required: false, default: '' },
    code: { type: String, required: true },
    defaultValue: { type: String, required: false, default: '' },
    jsCode: { type: String, required: false, default: '' },
    dataType: { type: String, required: false, default: '' }
  },
  setup(props, { emit }) {
    const current = ref(dataTypeCheck(props.defaultValue))
    const dictLabels = ref([])
    const { proxy } = getCurrentInstance()
    let jsF = jsCodeF(props.jsCode)

    function handleClick() {
      proxy.$refs.input.blur()
      emit('clicked', {
        [props.code]: jsF(current.value),
        changeTab: true
      })
    }

    /*参数类型处理*/
    function dataTypeCheck(val) {
      switch (props.dataType) {
        case 'String':
          break
        case 'Double':
          val = parseInt(val * 100) / 100
          break
        case 'Date':
          break
      }
      return val
    }

    return {
      handleClick,
      dictLabels,
      current
    }
  }
}
</script>
