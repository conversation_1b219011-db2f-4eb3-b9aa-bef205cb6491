<template>
  <div class="base-card__container ">
    <!-- 根据是否展示标题字段判断标题显示隐藏indexInfo.isName -->
    <div class="base-card__header">
      <div v-show="indexInfo.isName" class="blue-line" />
      <div v-show="indexInfo.isName" class="title">{{ indexInfo.indexName }}</div>
      <!-- <van-loading class="star-box" v-if="starLoading" color="#0089ff"></van-loading> -->
      <div v-show="indexInfo.isName" class="star-box">
        <img :src="starImg" @click="onCollect" />
      </div>
      <div v-if="collectionCount && !hideCollect" class="star-numbers">{{ collectionCount }}</div>
    </div>
    <!-- <CustomDivider v-if="indexInfo.isName" :margin-y="20" /> -->
    <!-- 查询条件 -->
    <IndicatorQuery v-if="query && query.length && !indexInfo.isCustomize" :query="query" @reload="reloadData" />
    <!--    <van-loading v-if="chartDataLoading" size="24px">加载中...</van-loading>-->
    <!--    <div v-else>-->
    <slot :indicator-data="indicatorData" style="column-count: 2" />
    <!--    </div>
    指标描述 -->
    <!-- <div class="base-card__desc" v-if="indicatorData.indexDesc">{{ indicatorData.indexDesc }}</div> -->
    <!-- 其他指标按钮 -->
    <!-- <div class="base-card__button-group" v-if="indexInfo.children">
      <button v-for="item in indexInfo.children" :key="item.id" class="base-card__button" @click="openDetail(item.id)">
        <span class="base-card__button-text">{{ item.name }}</span>
      </button>
    </div> -->
  </div>
</template>

<script>
import Vue from 'vue'
import { queryDataByApiIdApi } from '@/api/indicators'
import IndicatorQuery from './IndicatorQuery.vue'
import noCollectImg from '@/assets/component-img/no-collect.png'
import collectedImg from '@/assets/component-img/collected.png'
import { addCollectionApi, deleteCollectionApi } from '@/api/mine'

export default {
  name: 'BaseCard',
  components: {
    IndicatorQuery
  },
  props: {
    indexInfo: {
      type: Object,
      default: () => ({})
    },
    myCollectNum: {
      type: String,
      default: () => '0'
    },
    collectNum: {
      type: String,
      default: () => '0'
    },
    query: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      indicatorData: {},
      chartDataLoading: false,
      collectionStatus: this.myCollectNum === '1', // 指标是否收藏
      collectionCount: this.collectNum,
      hideCollect: false
    }
  },
  computed: {
    starImg() {
      return this.collectionStatus ? collectedImg : noCollectImg
    }
  },
  watch: {
    indexInfo: {
      deep: true,
      immediate: true,
      handler() {
        this.getIndicatorData(this.query)
      }
    }
  },
  mounted() {
    this.hideCollect = Vue.ls.get('HIDE_COLLECT')
  },
  // TODO: 尝试修改为挂载之后再去加载数据
  created() {
    // 没有查询条件的情况创建时即请求
    // if (this.query != null && !this.query.length) {
    //   console.log('数据：', this.query)
    //   this.getIndicatorData(this.query)
    // }
  },
  methods: {
    // 获取指标数据
    async getIndicatorData(queryParams = {}) {
      if (this.indexInfo.apiId) {
        let changeTab = queryParams.changeTab
        if (changeTab) delete queryParams.changeTab
        const data = {
          apiId: this.indexInfo.apiId,
          paramsMap: {
            ...queryParams
          }
        }
        this.chartDataLoading = true
        const res = await queryDataByApiIdApi(data)
        if (res.success) {
          res.result.changeTab = changeTab
          this.chartDataLoading = false
          this.indicatorData = res.result
        }
      }
    },
    openDetail(indexId) {
      this.$popup(indexId)
    },
    reloadData(queryParams) {
      this.getIndicatorData(queryParams)
    },
    onCollect() {
      // 判断收藏状态，已收藏下取消收藏，反之亦然
      if (this.collectionStatus) {
        this.deleteCollection()
      } else {
        this.addCollection()
      }
    },
    addCollection() {
      const data = {
        indexFrom: this.indicatorData.dataSource,
        indexId: this.indexInfo.id
      }
      this.starLoading = true
      addCollectionApi(data).then((res) => {
        if (res.success) {
          this.$toast.success('已收藏')
          this.collectionCount++
          this.collectionStatus = true
        }
        this.starLoading = false
      })
    },
    deleteCollection() {
      // hideTab()
      this.$dialog
        .confirm({
          title: '确认取消',
          message: '该操作将取消收藏该指标',
          className: 'delete-collection__dialog'
        })
        .then(() => {
          // on confirm
          this.starLoading = true
          deleteCollectionApi(this.indexInfo.id).then((res) => {
            if (res.success) {
              this.$toast.success('已取消收藏')
              this.collectionCount--
              this.collectionStatus = false
            }
            this.starLoading = false
          })
        })
        .catch(() => {
          // on cancel
        })
      // .finally(() => {
      //   showTab()
      // })
    }
  }
}
</script>

<style scoped>
.base-card__header {
  margin: 0;
}
</style>
