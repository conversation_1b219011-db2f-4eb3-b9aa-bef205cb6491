<template>
  <div class="search-container">
    <div class="query-wrapper" :style="{ maxHeight: fold ? '42vw' : 'unset' }">
      <div v-for="queryObj in query" :key="queryObj.code">
        <template v-if="queryObj.controlType === 'button' && queryObj.isShow === '1'">
          <IndicatorQueryButton
            :code="queryObj.code"
            :dict-code="queryObj.dictCode"
            :default-value="queryObj.defaultValue"
            :js-code="queryObj.jsCode"
            :data-type="queryObj.type"
            :options="queryObj.options"
            :options-api="queryObj.relApiId"
            @clicked="getDictValue"
          />
        </template>
        <template v-else-if="queryObj.controlType === 'input' && queryObj.isShow === '1'">
          <IndicatorQueryInput
            :name="queryObj.name"
            :code="queryObj.code"
            :default-value="queryObj.defaultValue"
            :js-code="queryObj.jsCode"
            :data-type="queryObj.type"
            @clicked="getDictValue"
          />
        </template>
        <template v-else-if="queryObj.controlType === 'select' && queryObj.isShow === '1'">
          <IndicatorQuerySelect
            :name="queryObj.name"
            :code="queryObj.code"
            :dict-code="queryObj.dictCode"
            :default-value="queryObj.defaultValue"
            :js-code="queryObj.jsCode"
            :data-type="queryObj.type"
            :options="queryObj.options"
            :options-api="queryObj.relApiId"
            @clicked="getDictValue"
          />
        </template>
        <template v-else-if="queryObj.controlType === 'date' && queryObj.isShow === '1'">
          <IndicatorQueryDate
            :name="queryObj.name"
            :code="queryObj.code"
            :default-value="queryObj.defaultValue"
            :data-format="queryObj.dataFormat"
            :js-code="queryObj.jsCode"
            :data-type="queryObj.type"
            @clicked="getDictValue"
          />
        </template>
      </div>

      <!-- <div class="query-buttons" style="width: 100%;">
        <button
          class="query-button"
          :class="{ active:  true}"
        >
          查询
        </button>
      </div> -->
    </div>
    <div v-if="queryCount > 3" class="fold" @click="fold = !fold">
      <svg v-if="fold" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="40" height="40">
        <path
          d="M512 661.333333c-12.8 0-21.333333-4.266667-29.866667-12.8L302.933333 469.333333c-17.066667-17.066667-17.066667-42.666667 0-59.733333s42.666667-17.066667 59.733334 0l149.333333 149.333333 149.333333-149.333333c17.066667-17.066667 42.666667-17.066667 59.733334 0s17.066667 42.666667 0 59.733333l-179.2 179.2c-8.533333 8.533333-17.066667 12.8-29.866667 12.8z"
          fill="#7c7c7c"
        ></path>
        <path
          d="M512 938.666667C277.333333 938.666667 85.333333 746.666667 85.333333 512S277.333333 85.333333 512 85.333333s426.666667 192 426.666667 426.666667-192 426.666667-426.666667 426.666667z m0-768c-187.733333 0-341.333333 153.6-341.333333 341.333333s153.6 341.333333 341.333333 341.333333 341.333333-153.6 341.333333-341.333333-153.6-341.333333-341.333333-341.333333z"
          fill="#A6AAAF"
        ></path>
      </svg>
      <svg v-else viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="40" height="40">
        <path
          d="M512 42.666667a469.333333 469.333333 0 1 0 469.333333 469.333333A469.333333 469.333333 0 0 0 512 42.666667z m0 864a394.666667 394.666667 0 1 1 394.666667-394.666667 395.146667 395.146667 0 0 1-394.666667 394.666667z"
          fill="#A6AAAF"
        ></path>
        <path
          d="M515.786667 462.986667L719.413333 666.666667a5.333333 5.333333 0 0 0 7.52 0l45.28-45.28a5.333333 5.333333 0 0 0 0-7.52L564.8 406.4 515.786667 357.333333a5.333333 5.333333 0 0 0-7.573334 0l-49.013333 49.066667-207.413333 207.413333a5.333333 5.333333 0 0 0 0 7.52l45.28 45.333334a5.333333 5.333333 0 0 0 7.52 0l203.626666-203.68a5.333333 5.333333 0 0 1 7.573334 0z"
          fill="#7c7c7c"
        ></path>
      </svg>
    </div>
  </div>
</template>

<script>
import { reactive, ref } from 'vue'
import IndicatorQueryButton from './IndicatorQueryButton.vue'
import IndicatorQueryInput from './IndicatorQueryInput.vue'
import IndicatorQuerySelect from './IndicatorQuerySelect.vue'
import IndicatorQueryDate from './IndicatorQueryDate.vue'
import { jsCodeF } from '@/utils'

export default {
  name: 'IndicatorQuery',
  components: {
    IndicatorQueryButton,
    IndicatorQueryInput,
    IndicatorQuerySelect,
    IndicatorQueryDate
  },
  props: {
    query: {
      type: Array,
      default: () => []
    }
  },
  setup(props, { emit }) {
    const defaultQueryParams = reactive({})
    const queryCount = ref(props.query.filter((v) => v.isShow === '1').length)
    const fold = ref(queryCount.value > 3)

    props.query.forEach((item) => {
      let jsF = jsCodeF(item.jsCode)
      defaultQueryParams[item.code] = jsF(item.defaultValue)
    })

    // 传递查询条件默认值
    setTimeout(() => {
      emit('reload', {
        ...defaultQueryParams
      })
    }, 30)

    function getDictValue(paramObj) {
      emit('reload', {
        ...defaultQueryParams,
        ...paramObj
      })
      emit('search', paramObj)
    }

    return {
      fold,
      queryCount,
      getDictValue,
      defaultQueryParams
    }
  }
}
</script>
<style lang="scss" scoped>
.search-container {
  .query-wrapper {
    overflow: hidden;
  }

  .fold {
    background-color: white;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
