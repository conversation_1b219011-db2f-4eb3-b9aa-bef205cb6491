<template>
  <div class="query-search">
    <van-field
      readonly
      clickable
      :label="name"
      :value="dictLabels[tabI].text"
      placeholder="请选择"
      @click="showPicker = true"
    />
    <van-popup v-model="showPicker" round position="bottom">
      <van-picker
        show-toolbar
        :columns="dictLabels"
        value-key="text"
        @cancel="showPicker = false"
        @confirm="handleClick"
      />
    </van-popup>
  </div>
</template>

<script>
import { ref, watch } from 'vue'
import { getDictLabelByCodeApi, queryDataByApiIdApi } from '@/api/indicators'
import { jsCodeF } from '@/utils'

export default {
  name: 'IndicatorQuerySelect',
  props: {
    name: { type: String, required: false, default: '' },
    code: { type: String, required: true, default: '' },
    dictCode: { type: String, required: false, default: '' },
    defaultValue: { type: [String, Number], required: false, default: '' },
    jsCode: { type: String, required: false, default: '' },
    dataType: { type: String, required: false, default: '' },
    options: { type: Array, required: false, default: () => [] },
    optionsApi: { type: String, required: false, default: '' }
  },
  setup(props, { emit }) {
    const current = ref(dataTypeCheck(props.defaultValue))
    const showPicker = ref(false)
    const dictLabels = ref([{}])
    const tabI = ref(0)
    let jsF = jsCodeF(props.jsCode)

    function handleClick(dictValue, i) {
      current.value = dataTypeCheck(dictValue.value)
      tabI.value = i
      showPicker.value = false
    }

    watch(current, (newVal) => {
      emit('clicked', {
        [props.code]: jsF(newVal),
        changeTab: true
      })
    })

    watch(
      () => props.options,
      (newVal) => {
        dictLabels.value = [...newVal]
        if (newVal.findIndex((v) => v.value === '') < 0) {
          dictLabels.value.unshift({ text: '默认', value: '' })
        }
        // current.value = '';
        tabI.value = 0
      },
      { deep: true }
    )

    function getOptionsApi(apiId) {
      if (!apiId) return false
      queryDataByApiIdApi({ apiId })
        .then((res) => res.result)
        .then((res) => {
          dictLabels.value = res.data.map((v) => ({ text: v[res.column[0]], value: v[res.column[1]] }))
          if (!props.defaultValue) dictLabels.value.unshift({ text: '默认', value: '' })
        })
    }

    async function getDictLabelByCode() {
      if (!props.dictCode) return false
      const arr = await getDictLabelByCodeApi(props.dictCode).then((res) => res.result)
      dictLabels.value = arr
      for (let i = 0; i < arr.length; i++) {
        if (props.defaultValue === arr[i].value) {
          tabI.value = i
          break
        }
      }
    }

    /*参数类型处理*/
    function dataTypeCheck(val) {
      switch (props.dataType) {
        case 'String':
          break
        case 'Double':
          val = parseInt(val * 100) / 100
          break
        case 'Date':
          break
      }
      return val
    }

    if (props.options?.length > 0) {
      dictLabels.value = props.options
      if (props.defaultValue === '') {
        dictLabels.value.unshift({ text: '默认', value: '' })
        current.value = ''
      } else tabI.value = props.options.findIndex((v) => v.value === props.defaultValue)
    } else if (props.optionsApi) {
      getOptionsApi(props.optionsApi)
    } else getDictLabelByCode()

    return {
      handleClick,
      dictLabels,
      current,
      showPicker,
      tabI
    }
  }
}
</script>
