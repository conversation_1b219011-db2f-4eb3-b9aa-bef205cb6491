<template>
  <div class="query-search">
    <van-field readonly clickable :label="name" :value="current" placeholder="请选择" @click="showPicker = true" />
    <van-popup v-model="showPicker" round position="bottom">
      <van-picker
        v-if="dataFormat === 'year'"
        v-model="current"
        value-key="label"
        show-toolbar
        :columns="yearList"
        @cancel="showPicker = false"
        @confirm="handleClick"
      />
      <van-datetime-picker
        v-else
        :type="dataFormat"
        title="选择日期"
        @cancel="showPicker = false"
        @confirm="handleClick"
      />
    </van-popup>
  </div>
</template>

<script>
import { ref } from 'vue'
import { jsCodeF } from '@/utils'

export default {
  name: 'IndicatorQueryDate',
  props: {
    name: { type: String, required: false, default: '' },
    code: { type: String, required: true },
    defaultValue: { type: [String, Number, Date], required: false, default: '' },
    dataFormat: { type: String, required: true },
    jsCode: { type: String, required: false, default: '' },
    dataType: { type: String, required: false, default: '' }
  },
  setup(props, { emit }) {
    const current = ref(dataTypeCheck(props.defaultValue))
    const dictLabels = ref([])
    const showPicker = ref(false)
    const yearList = []
    let jsF = jsCodeF(props.jsCode)
    for (let i = 2010; i < new Date().getFullYear() + 1; i++) {
      yearList.push({ label: i, value: i })
    }
    if (props.dataFormat === 'year' && !props.defaultValue) {
      yearList.unshift({ label: '默认', value: '' })
      current.value = '默认'
    }

    function handleClick(date) {
      let val
      if (props.dataFormat === 'year') {
        val = date.value
      } else {
        let y = date.getFullYear(),
          m = (date.getMonth() + 1).toString().padStart(2, '0'),
          d = date.getDate().toString().padStart(2, '0'),
          h = date.getHours().toString().padStart(2, '0')
        switch (props.dataFormat) {
          case 'year-month':
            val = `${y}-${m}`
            break
          case 'datehour':
            val = `${y}-${m}-${d}:${h}`
            break
          default:
            val = `${y}-${m}-${d}`
        }
      }
      current.value = dataTypeCheck(val)
      showPicker.value = false
      emit('clicked', {
        [props.code]: jsF(val),
        changeTab: true
      })
    }

    /*参数类型处理*/
    function dataTypeCheck(val) {
      switch (props.dataType) {
        case 'String':
          break
        case 'Double':
          val = parseInt(val * 100) / 100
          break
        case 'Date':
          break
      }
      return val
    }

    return {
      yearList,
      handleClick,
      dictLabels,
      current,
      showPicker
    }
  }
}
</script>
