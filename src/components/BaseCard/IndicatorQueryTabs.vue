<template>
  <div class="tabs-container">
    <van-tabs :active="active" scrollspy :ellipsis="false" :before-change="beforeTabChange" @change="change">
      <van-tab v-for="(tab, index) in tabList" :key="index" :title="tab.name"></van-tab>
    </van-tabs>
  </div>
</template>

<script>
export default {
  name: 'IndicatorQueryTabs',
  props: {
    tabList: { type: Array, required: true, default: () => [] }
  },
  data() {
    return {
      active: 0
    }
  },
  methods: {
    // 添加 beforeChange 钩子
    beforeTabChange(index) {
      return new Promise((resolve) => {
        this.$nextTick(() => {
          resolve(true)
        })
      })
    },
    change(value) {
      this.$nextTick(() => {
        this.active = value
        this.$emit('tabsChange', value)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.tabs-container {
  overflow-x: hidden;

  .tabs {
    position: relative;
    display: flex;
    width: 100%;
    background: #fff;
    padding: 20px;
    justify-content: space-around;
    align-items: center;
    overflow-x: auto; /* 允许水平滚动 */
    white-space: nowrap; /* 防止换行 */
    -webkit-overflow-scrolling: touch; /* 移动端平滑滚动 */
    scrollbar-width: none; /* Firefox隐藏滚动条 */
    scroll-padding-left: 20px; /* 左侧保留20px空间 */
    scroll-padding-right: 20px; /* 右侧保留20px空间 */

    &::-webkit-scrollbar {
      display: none; /* 隐藏Chrome和Safari的滚动条 */
    }
    > div {
      height: 59px;
      display: flex;
      margin-left: 10px;
      justify-content: center;
      align-items: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28px;
      color: #333333;
      line-height: 42px;
      text-align: center;
      font-style: normal;
      border-radius: 40px;
      padding: 0 20px;
      background: hsla(0, 0%, 96%, 1);
    }
    .active-tab {
      background-color: hsla(206, 100%, 50%, 1);
      color: #ffffff;
    }
    & div:first-child {
      margin-left: 0px;
    }
  }
  ::v-deep .van-tabs {
    width: 100%;
    margin-bottom: 10px;
    font-family: PingFang, serif;
    font-size: 16px;

    .van-tabs__wrap {
      height: 34px;
      padding: 0 10px; // 添加内边距，防止贴边
      border-bottom: none;
    }

    .van-tabs__nav {
      // white-space: nowrap; // 确保标签在一行
    }
  }

  ::v-deep .van-tabs__line {
    display: none;
  }

  ::v-deep .van-tab {
    background-color: #f7f8fa;
    color: #333;
    border-radius: 40px;
    padding: 0 20px !important;
    margin: 0 5px; // 减小边距，防止溢出
    flex-shrink: 0; // 防止标签被压缩
  }

  ::v-deep .van-tab--active {
    color: #165dff;
    font-weight: normal !important;
    background-color: #e8f3ff;
  }
}
</style>
