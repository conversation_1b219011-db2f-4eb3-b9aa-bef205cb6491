<template>
  <div class="query-buttons">
    <button
      v-for="dict of dictLabels"
      :key="dict.value"
      class="query-button"
      :class="{ active: dict.value === current }"
      @click="handleClick(dict.value)"
    >
      {{ dict.text }}
    </button>
  </div>
</template>

<script>
import { ref, watch } from 'vue'
import { getDictLabelByCodeApi, queryDataByApiIdApi } from '@/api/indicators'
import { jsCodeF } from '@/utils'

export default {
  name: 'IndicatorQueryButton',
  props: {
    code: { type: String, required: true },
    dictCode: { type: String, required: false, default: '' },
    defaultValue: { type: [Number, String], required: true },
    jsCode: { type: String, required: false, default: '' },
    dataType: { type: String, required: false, default: '' },
    options: { type: Array, required: false, default: () => [] },
    optionsApi: { type: String, required: false, default: '' }
  },
  setup(props, { emit }) {
    const current = ref(dataTypeCheck(props.defaultValue))
    const dictLabels = ref([])
    let jsF = jsCodeF(props.jsCode)

    function handleClick(dictValue) {
      current.value = dataTypeCheck(dictValue)
    }

    watch(current, (newVal) => {
      emit('clicked', {
        [props.code]: jsF(newVal),
        changeTab: true
      })
    })

    function getOptionsApi(apiId) {
      if (!apiId) return false
      queryDataByApiIdApi({ apiId })
        .then((res) => res.result)
        .then((res) => {
          dictLabels.value = res.data.map((v) => ({ text: v[res.column[0]], value: v[res.column[1]] }))
          if (!props.defaultValue) dictLabels.value.unshift({ text: '默认', value: '' })
        })
    }

    async function getDictLabelByCode() {
      const res = await getDictLabelByCodeApi(props.dictCode)
      dictLabels.value = res.result
    }

    /*参数类型处理*/
    function dataTypeCheck(val) {
      switch (props.dataType) {
        case 'String':
          break
        case 'Double':
          val = parseInt(val * 100) / 100
          break
        case 'Date':
          break
      }
      return val
    }

    if (props.options.length) {
      dictLabels.value = props.options
    } else if (props.optionsApi) {
      getOptionsApi(props.optionsApi)
    } else getDictLabelByCode()

    return {
      handleClick,
      dictLabels,
      current
    }
  }
}
</script>
