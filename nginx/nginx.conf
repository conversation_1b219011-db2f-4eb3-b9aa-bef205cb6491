
user  nginx;
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;
    client_max_body_size 1100m;
    proxy_connect_timeout 5;
	proxy_read_timeout 120;
	proxy_send_timeout 10;
	proxy_buffer_size 16k;
	proxy_buffers 4 64k;
	proxy_busy_buffers_size 128k;
	proxy_temp_file_write_size 128k;
	gzip on;
	gzip_buffers 32 4K;
	gzip_comp_level 6;
	gzip_min_length 100;
	gzip_types application/javascript text/css text/xml;
	gzip_disable "MSIE [1-6]\.";
	gzip_vary on;
    map $http_upgrade $connection_upgrade { 
        default          keep-alive;  #默认为keep-alive 可以支持 一般http请求
        'websocket'      upgrade;     #如果为websocket 则为 upgrade 可升级的。
    }
    server {
		listen       9991;
		  location ^~/situation{
 		     proxy_pass http://*************:9095;
 		     proxy_set_header Host $proxy_host;
 		     proxy_set_header X-Real-IP $remote_addr;
 		     proxy_set_header REMOTE-HOST $remote_addr;
 		     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
 		     proxy_http_version 1.1;
 		     proxy_set_header Upgrade $http_upgrade;
 		     proxy_set_header Connection $connection_upgrade;
 	      }
 
 	    location ^~/situation{
 		     proxy_pass http://*************:9098;
 		     proxy_set_header Host $proxy_host;
 		     proxy_set_header X-Real-IP $remote_addr;
 		     proxy_set_header REMOTE-HOST $remote_addr;
 		     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
 		     proxy_http_version 1.1;
 		     proxy_set_header Upgrade $http_upgrade;
 		     proxy_set_header Connection $connection_upgrade;
 	    }
		location / {
			root   /etc/nginx/html/;
			index  index.html index.htm;
			try_files $uri $uri/ /index.html;
		}

	}
}
